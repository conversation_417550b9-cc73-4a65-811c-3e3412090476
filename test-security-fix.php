<?php
/**
 * ملف اختبار لحل مشكلة رمز الأمان
 * 
 * هذا الملف يختبر الدوال الجديدة للتأكد من عملها
 */

// منع الوصول المباشر
if (!defined('ABSPATH')) {
    exit;
}

/**
 * اختبار دالة تجديد رمز الأمان
 */
function test_nonce_refresh() {
    echo "<h3>اختبار تجديد رمز الأمان</h3>";
    
    // إنشاء رمز أمان جديد
    $nonce = wp_create_nonce('pexlat_form_nonce');
    echo "<p>رمز الأمان الجديد: " . substr($nonce, 0, 10) . "...</p>";
    
    // اختبار التحقق من رمز الأمان
    $is_valid = wp_verify_nonce($nonce, 'pexlat_form_nonce');
    echo "<p>صحة رمز الأمان: " . ($is_valid ? 'صحيح' : 'خاطئ') . "</p>";
    
    return $nonce;
}

/**
 * اختبار دالة اكتشاف استضافة DZSecurity
 */
function test_dzsecurity_detection() {
    echo "<h3>اختبار اكتشاف استضافة DZSecurity</h3>";
    
    // محاكاة headers
    $headers_to_check = array(
        'HTTP_CF_RAY',
        'HTTP_X_FORWARDED_FOR',
        'HTTP_X_REAL_IP'
    );
    
    $detected = false;
    foreach ($headers_to_check as $header) {
        if (isset($_SERVER[$header])) {
            $detected = true;
            echo "<p>تم اكتشاف header: $header</p>";
        }
    }
    
    $server_name = $_SERVER['SERVER_NAME'] ?? '';
    if (strpos($server_name, 'dzsecurity') !== false) {
        $detected = true;
        echo "<p>تم اكتشاف DZSecurity في اسم الخادم</p>";
    }
    
    echo "<p>حالة الاكتشاف: " . ($detected ? 'تم اكتشاف DZSecurity' : 'لم يتم اكتشاف DZSecurity') . "</p>";
    
    return $detected;
}

/**
 * اختبار تنظيف الكاش
 */
function test_cache_clearing() {
    echo "<h3>اختبار تنظيف الكاش</h3>";
    
    // تنظيف كاش WordPress
    wp_cache_flush();
    echo "<p>تم تنظيف كاش WordPress</p>";
    
    // تنظيف كاش الكائن إذا كان متاحاً
    if (function_exists('wp_cache_delete')) {
        wp_cache_delete('nonce_' . wp_nonce_tick(), 'nonces');
        echo "<p>تم تنظيف كاش الكائن</p>";
    }
    
    // تنظيف OPcache إذا كان متاحاً
    if (function_exists('opcache_reset')) {
        opcache_reset();
        echo "<p>تم تنظيف OPcache</p>";
    } else {
        echo "<p>OPcache غير متاح</p>";
    }
}

/**
 * تشغيل جميع الاختبارات
 */
function run_security_tests() {
    if (!current_user_can('manage_options')) {
        wp_die('غير مصرح لك بالوصول لهذه الصفحة');
    }
    
    echo "<div style='padding: 20px; font-family: Arial, sans-serif;'>";
    echo "<h2>اختبار حلول مشكلة رمز الأمان</h2>";
    
    test_nonce_refresh();
    test_dzsecurity_detection();
    test_cache_clearing();
    
    echo "<h3>معلومات الخادم</h3>";
    echo "<p>اسم الخادم: " . ($_SERVER['SERVER_NAME'] ?? 'غير محدد') . "</p>";
    echo "<p>User Agent: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'غير محدد') . "</p>";
    echo "<p>عنوان IP: " . ($_SERVER['REMOTE_ADDR'] ?? 'غير محدد') . "</p>";
    
    echo "<h3>إعدادات WordPress</h3>";
    echo "<p>إصدار WordPress: " . get_bloginfo('version') . "</p>";
    echo "<p>URL الموقع: " . home_url() . "</p>";
    echo "<p>URL الإدارة: " . admin_url() . "</p>";
    
    echo "</div>";
}

// تشغيل الاختبارات إذا تم طلبها
if (isset($_GET['test_security']) && $_GET['test_security'] === '1') {
    add_action('wp_loaded', 'run_security_tests');
}

/**
 * إضافة رابط الاختبار في لوحة الإدارة
 */
function add_security_test_link() {
    if (current_user_can('manage_options')) {
        add_submenu_page(
            'pexlat-form',
            'اختبار الأمان',
            'اختبار الأمان',
            'manage_options',
            'pexlat-form-security-test',
            'display_security_test_page'
        );
    }
}
add_action('admin_menu', 'add_security_test_link');

/**
 * عرض صفحة اختبار الأمان
 */
function display_security_test_page() {
    ?>
    <div class="wrap">
        <h1>اختبار حلول مشكلة رمز الأمان</h1>
        <p>هذه الصفحة تختبر الحلول المطبقة لمشكلة "رمز الأمان غير صحيح"</p>
        
        <div class="card">
            <h2>اختبار سريع</h2>
            <p>انقر على الزر أدناه لتشغيل اختبار شامل:</p>
            <a href="<?php echo home_url('?test_security=1'); ?>" class="button button-primary" target="_blank">
                تشغيل الاختبار
            </a>
        </div>
        
        <div class="card">
            <h2>اختبار AJAX</h2>
            <p>اختبار دالة تجديد رمز الأمان عبر AJAX:</p>
            <button id="test-nonce-refresh" class="button">اختبار تجديد رمز الأمان</button>
            <div id="test-result" style="margin-top: 10px;"></div>
        </div>
    </div>
    
    <script>
    jQuery(document).ready(function($) {
        $('#test-nonce-refresh').click(function() {
            var $button = $(this);
            var $result = $('#test-result');
            
            $button.prop('disabled', true).text('جاري الاختبار...');
            $result.html('<p>جاري اختبار تجديد رمز الأمان...</p>');
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'pexlat_form_refresh_nonce'
                },
                success: function(response) {
                    if (response.success) {
                        $result.html('<div class="notice notice-success"><p>✅ نجح الاختبار! تم تجديد رمز الأمان بنجاح.</p><p>رمز الأمان الجديد: ' + response.data.nonce.substring(0, 10) + '...</p></div>');
                    } else {
                        $result.html('<div class="notice notice-error"><p>❌ فشل الاختبار: ' + (response.data || 'خطأ غير معروف') + '</p></div>');
                    }
                },
                error: function(xhr, status, error) {
                    $result.html('<div class="notice notice-error"><p>❌ خطأ في الاتصال: ' + error + '</p></div>');
                },
                complete: function() {
                    $button.prop('disabled', false).text('اختبار تجديد رمز الأمان');
                }
            });
        });
    });
    </script>
    <?php
}
?>
